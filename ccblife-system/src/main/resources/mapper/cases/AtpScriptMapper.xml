<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ccblife.cases.mapper.AtpScriptMapper">
    
    <resultMap type="AtpScript" id="AtpScriptResult">
        <result property="scriptId"    column="script_id"    />
        <result property="scenarioCode"    column="scenario_code"    />
        <result property="channelCode"    column="channel_code"    />
        <result property="scriptName"    column="script_name"    />
        <result property="scriptShortName"    column="script_short_name"    />
        <result property="scriptPath"    column="script_path"    />
        <result property="templatePath"    column="template_path"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAtpScriptVo">
        select script_id, scenario_code, channel_code, script_name, script_short_name, script_path, template_path, del_flag, create_by, create_time, update_by, update_time, remark from atp_script
    </sql>

    <select id="selectAtpScriptList" parameterType="AtpScript" resultMap="AtpScriptResult">
        <include refid="selectAtpScriptVo"/>
        <where>  
            <if test="scenarioCode != null  and scenarioCode != ''"> and scenario_code = #{scenarioCode}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="scriptName != null  and scriptName != ''"> and script_name like concat('%', #{scriptName}, '%')</if>
            <if test="scriptShortName != null  and scriptShortName != ''"> and script_short_name like concat('%', #{scriptShortName}, '%')</if>
        </where>
    </select>
    
    <select id="selectAtpScriptByScriptId" parameterType="Long" resultMap="AtpScriptResult">
        <include refid="selectAtpScriptVo"/>
        where script_id = #{scriptId}
    </select>

    <insert id="insertAtpScript" parameterType="AtpScript" useGeneratedKeys="true" keyProperty="scriptId">
        insert into atp_script
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scenarioCode != null and scenarioCode != ''">scenario_code,</if>
            <if test="channelCode != null and channelCode != ''">channel_code,</if>
            <if test="scriptName != null and scriptName != ''">script_name,</if>
            <if test="scriptShortName != null and scriptShortName != ''">script_short_name,</if>
            <if test="scriptPath != null">script_path,</if>
            <if test="templatePath != null">template_path,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scenarioCode != null and scenarioCode != ''">#{scenarioCode},</if>
            <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
            <if test="scriptName != null and scriptName != ''">#{scriptName},</if>
            <if test="scriptShortName != null and scriptShortName != ''">#{scriptShortName},</if>
            <if test="scriptPath != null">#{scriptPath},</if>
            <if test="templatePath != null">#{templatePath},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAtpScript" parameterType="AtpScript">
        update atp_script
        <trim prefix="SET" suffixOverrides=",">
            <if test="scenarioCode != null and scenarioCode != ''">scenario_code = #{scenarioCode},</if>
            <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
            <if test="scriptName != null and scriptName != ''">script_name = #{scriptName},</if>
            <if test="scriptShortName != null and scriptShortName != ''">script_short_name = #{scriptShortName},</if>
            <if test="scriptPath != null">script_path = #{scriptPath},</if>
            <if test="templatePath != null">template_path = #{templatePath},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where script_id = #{scriptId}
    </update>

    <delete id="deleteAtpScriptByScriptId" parameterType="Long">
        delete from atp_script where script_id = #{scriptId}
    </delete>

    <delete id="deleteAtpScriptByScriptIds" parameterType="String">
        delete from atp_script where script_id in 
        <foreach item="scriptId" collection="array" open="(" separator="," close=")">
            #{scriptId}
        </foreach>
    </delete>
</mapper>