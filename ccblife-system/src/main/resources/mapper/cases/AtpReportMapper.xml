<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ccblife.cases.mapper.AtpReportMapper">
    
    <resultMap type="AtpReport" id="AtpReportResult">
        <result property="reportId"    column="report_id"    />
        <result property="scriptId"    column="script_id"    />
        <result property="reportName"    column="report_name"    />
        <result property="execEnvCode"    column="exec_env_code"    />
        <result property="execStartTime"    column="exec_start_time"    jdbcType="TIMESTAMP" />
        <result property="execEndTime"    column="exec_end_time"    jdbcType="TIMESTAMP" />
        <result property="execStatusCode"    column="exec_status_code"    />
        <result property="totalCases"    column="total_cases"    />
        <result property="passCases"    column="pass_cases"    />
        <result property="failCases"    column="fail_cases"    />
        <result property="passRate"    column="pass_rate"    />
        <result property="failReason"    column="fail_reason"    />
        <result property="execScriptPath"    column="exec_script_path"    />
        <result property="execReportPath"    column="exec_report_path"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectAtpReportVo">
        select report_id, script_id, report_name, exec_env_code, exec_start_time, exec_end_time, exec_status_code, total_cases, pass_cases, fail_cases, pass_rate, fail_reason, exec_script_path, exec_report_path, create_by, create_time from atp_report
    </sql>

    <select id="selectAtpReportList" parameterType="AtpReport" resultMap="AtpReportResult">
        <include refid="selectAtpReportVo"/>
        <where>  
            <if test="scriptId != null "> and script_id like concat('%', #{scriptId}, '%')</if>
            <if test="reportName != null  and reportName != ''"> and report_name like concat('%', #{reportName}, '%')</if>
            <if test="execEnvCode != null  and execEnvCode != ''"> and exec_env_code = #{execEnvCode}</if>
            <if test="params.beginExecStartTime != null and params.beginExecStartTime != '' and params.endExecStartTime != null and params.endExecStartTime != ''"> and exec_start_time between to_timestamp(#{params.beginExecStartTime}, 'yyyy-MM-dd') and to_timestamp(#{params.endExecStartTime}, 'yyyy-MM-dd')</if>
            <if test="execStatusCode != null  and execStatusCode != ''"> and exec_status_code = #{execStatusCode}</if>
        </where>
    </select>
    
    <select id="selectAtpReportByReportId" parameterType="Long" resultMap="AtpReportResult">
        <include refid="selectAtpReportVo"/>
        where report_id = #{reportId}
    </select>

    <insert id="insertAtpReport" parameterType="AtpReport" useGeneratedKeys="true" keyProperty="reportId">
        insert into atp_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scriptId != null">script_id,</if>
            <if test="reportName != null and reportName != ''">report_name,</if>
            <if test="execEnvCode != null and execEnvCode != ''">exec_env_code,</if>
            <if test="execStartTime != null">exec_start_time,</if>
            <if test="execEndTime != null">exec_end_time,</if>
            <if test="execStatusCode != null and execStatusCode != ''">exec_status_code,</if>
            <if test="totalCases != null">total_cases,</if>
            <if test="passCases != null">pass_cases,</if>
            <if test="failCases != null">fail_cases,</if>
            <if test="passRate != null">pass_rate,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="execScriptPath != null">exec_script_path,</if>
            <if test="execReportPath != null">exec_report_path,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scriptId != null">#{scriptId},</if>
            <if test="reportName != null and reportName != ''">#{reportName},</if>
            <if test="execEnvCode != null and execEnvCode != ''">#{execEnvCode},</if>
            <if test="execStartTime != null">#{execStartTime},</if>
            <if test="execEndTime != null">#{execEndTime},</if>
            <if test="execStatusCode != null and execStatusCode != ''">#{execStatusCode},</if>
            <if test="totalCases != null">#{totalCases},</if>
            <if test="passCases != null">#{passCases},</if>
            <if test="failCases != null">#{failCases},</if>
            <if test="passRate != null">#{passRate},</if>
            <if test="failReason != null">#{failReason},</if>
            <if test="execScriptPath != null">#{execScriptPath},</if>
            <if test="execReportPath != null">#{execReportPath},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateAtpReport" parameterType="AtpReport">
        update atp_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="scriptId != null">script_id = #{scriptId},</if>
            <if test="reportName != null and reportName != ''">report_name = #{reportName},</if>
            <if test="execEnvCode != null and execEnvCode != ''">exec_env_code = #{execEnvCode},</if>
            <if test="execStartTime != null">exec_start_time = #{execStartTime},</if>
            <if test="execEndTime != null">exec_end_time = #{execEndTime},</if>
            <if test="execStatusCode != null and execStatusCode != ''">exec_status_code = #{execStatusCode},</if>
            <if test="totalCases != null">total_cases = #{totalCases},</if>
            <if test="passCases != null">pass_cases = #{passCases},</if>
            <if test="failCases != null">fail_cases = #{failCases},</if>
            <if test="passRate != null">pass_rate = #{passRate},</if>
            <if test="failReason != null">fail_reason = #{failReason},</if>
            <if test="execScriptPath != null">exec_script_path = #{execScriptPath},</if>
            <if test="execReportPath != null">exec_report_path = #{execReportPath},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where report_id = #{reportId}
    </update>

    <delete id="deleteAtpReportByReportId" parameterType="Long">
        delete from atp_report where report_id = #{reportId}
    </delete>

    <delete id="deleteAtpReportByReportIds" parameterType="String">
        delete from atp_report where report_id in 
        <foreach item="reportId" collection="array" open="(" separator="," close=")">
            #{reportId}
        </foreach>
    </delete>
</mapper>