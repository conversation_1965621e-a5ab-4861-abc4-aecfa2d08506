package com.ccblife.cases.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import com.ccblife.cases.domain.AtpScript;
import com.ccblife.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JMeter脚本执行工具类
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public class JMeterExecutorUtil {
    
    private static final Logger log = LoggerFactory.getLogger(JMeterExecutorUtil.class);
    
    // 正在执行的任务管理
    private static final Map<Long, Process> runningTasks = new ConcurrentHashMap<>();
    
    // Windows环境配置
    private static final String WINDOWS_BASE_PATH = "E:\\Project\\Temp";
    private static final String WINDOWS_JMETER_BIN = WINDOWS_BASE_PATH + "\\apache-jmeter-5.6.3\\bin";
    private static final String WINDOWS_ANT_BIN = WINDOWS_BASE_PATH + "\\apache-jmeter-5.6.3\\apache-ant-1.10.15\\bin";
    private static final String WINDOWS_SCRIPT_DIR = WINDOWS_BASE_PATH + "\\jmeter\\scripts";
    private static final String WINDOWS_RESULT_DIR = WINDOWS_BASE_PATH + "\\jmeter\\results";
    private static final String WINDOWS_REPORT_DIR = WINDOWS_BASE_PATH + "\\jmeter\\reports";
    
    // Linux环境配置
    private static final String LINUX_BASE_PATH = "/app/temp";
    private static final String LINUX_JMETER_BIN = LINUX_BASE_PATH + "/apache-jmeter-5.6.3/bin";
    private static final String LINUX_ANT_BIN = LINUX_BASE_PATH + "/apache-jmeter-5.6.3/apache-ant-1.10.15/bin";
    private static final String LINUX_SCRIPT_DIR = LINUX_BASE_PATH + "/jmeter/scripts";
    private static final String LINUX_RESULT_DIR = LINUX_BASE_PATH + "/jmeter/results";
    private static final String LINUX_REPORT_DIR = LINUX_BASE_PATH + "/jmeter/reports";
    
    // 超时配置
    private static final int JMETER_TIMEOUT_SECONDS = 600; // 10分钟
    private static final int ANT_TIMEOUT_SECONDS = 180;    // 3分钟
    
    /**
     * JMeter执行配置类
     */
    public static class JMeterConfig {
        private String scriptPath;      // JMX脚本路径
        private String casePath;        // 测试用例文件路径
        private String resultPath;      // 结果XML文件路径
        private String reportPath;      // 报告目录路径
        private String env;             // 环境参数
        private String reportName;      // 报告名称
        
        // 构造函数
        public JMeterConfig(String scriptPath, String casePath, String resultPath, String reportPath, String env, String reportName) {
            this.scriptPath = scriptPath;
            this.casePath = casePath;
            this.resultPath = resultPath;
            this.reportPath = reportPath;
            this.env = env;
            this.reportName = reportName;
        }
        
        // Getters
        public String getScriptPath() { return scriptPath; }
        public String getCasePath() { return casePath; }
        public String getResultPath() { return resultPath; }
        public String getReportPath() { return reportPath; }
        public String getEnv() { return env; }
        public String getReportName() { return reportName; }
    }
    
    /**
     * 检测是否为Windows环境
     */
    public static boolean isWindowsEnvironment() {
        String osName = System.getProperty("os.name");
        return osName != null && osName.toLowerCase().contains("windows");
    }
    
    /**
     * 根据AtpScript创建JMeter配置
     */
    public static JMeterConfig createJMeterConfig(AtpScript script, String env, String caseFileName) {
        boolean isWindows = isWindowsEnvironment();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String reportName = script.getScriptShortName() + "_" + timestamp;
        
        if (isWindows) {
            String scriptPath = WINDOWS_SCRIPT_DIR + "\\" + getJmxFileName(script);
            String casePath = WINDOWS_SCRIPT_DIR + "\\" + caseFileName;
            String resultPath = WINDOWS_RESULT_DIR + "\\" + reportName + ".xml";
            String reportPath = WINDOWS_REPORT_DIR + "\\" + reportName;
            
            return new JMeterConfig(scriptPath, casePath, resultPath, reportPath, env, reportName);
        } else {
            String scriptPath = LINUX_SCRIPT_DIR + "/" + getJmxFileName(script);
            String casePath = LINUX_SCRIPT_DIR + "/" + caseFileName;
            String resultPath = LINUX_RESULT_DIR + "/" + reportName + ".xml";
            String reportPath = LINUX_REPORT_DIR + "/" + reportName;
            
            return new JMeterConfig(scriptPath, casePath, resultPath, reportPath, env, reportName);
        }
    }
    
    /**
     * 从AtpScript获取JMX文件名
     */
    private static String getJmxFileName(AtpScript script) {
        if (StringUtils.isNotEmpty(script.getScriptPath())) {
            // 如果scriptPath包含完整路径，提取文件名
            String path = script.getScriptPath();
            if (path.contains("\\") || path.contains("/")) {
                return path.substring(path.lastIndexOf(path.contains("\\") ? "\\" : "/") + 1);
            }
            return path;
        }
        // 如果没有指定路径，使用默认命名规则
        return script.getScriptShortName() + ".jmx";
    }
    
    /**
     * 执行JMeter脚本
     */
    public static String executeJMeterScript(AtpScript script, JMeterConfig config) throws Exception {
        Long scriptId = script.getScriptId();
        log.info("开始执行JMeter脚本: {} (ID: {})", script.getScriptName(), scriptId);
        
        // 检查是否已有任务在运行
        if (runningTasks.containsKey(scriptId)) {
            throw new RuntimeException("脚本正在执行中，请稍后再试");
        }
        
        try {
            // 创建必要的目录
            createDirectories(config);
            
            // 执行脚本
            boolean isWindows = isWindowsEnvironment();
            if (isWindows) {
                return executeWindowsScript(scriptId, config);
            } else {
                return executeLinuxScript(scriptId, config);
            }
        } finally {
            // 清理运行任务记录
            runningTasks.remove(scriptId);
        }
    }
    
    /**
     * 终止正在执行的任务
     */
    public static boolean terminateTask(Long scriptId) {
        Process process = runningTasks.get(scriptId);
        if (process != null && process.isAlive()) {
            log.info("终止脚本执行任务: {}", scriptId);
            process.destroyForcibly();
            runningTasks.remove(scriptId);
            return true;
        }
        return false;
    }
    
    /**
     * 检查任务是否正在运行
     */
    public static boolean isTaskRunning(Long scriptId) {
        Process process = runningTasks.get(scriptId);
        return process != null && process.isAlive();
    }
    
    /**
     * 创建必要的目录
     */
    private static void createDirectories(JMeterConfig config) throws IOException {
        // 创建结果目录
        Path resultDir = Paths.get(config.getResultPath()).getParent();
        if (!Files.exists(resultDir)) {
            Files.createDirectories(resultDir);
        }
        
        // 创建报告目录
        Path reportDir = Paths.get(config.getReportPath());
        if (!Files.exists(reportDir)) {
            Files.createDirectories(reportDir);
        }
    }
    
    /**
     * 执行Windows环境下的JMeter脚本
     */
    private static String executeWindowsScript(Long scriptId, JMeterConfig config) throws Exception {
        StringBuilder result = new StringBuilder();
        
        // 删除旧的结果文件
        deleteFileIfExists(config.getResultPath());
        result.append("删除旧结果文件: ").append(config.getResultPath()).append("\n");
        
        // 1. 执行JMeter命令
        String jmeterCommand = String.format(
            "cmd /c cd /d \"%s\" && call .\\jmeter.bat -n -t \"%s\" -Jenv=%s -Jcase_path=\"%s\" -l \"%s\" -Jsample_variables=dex,casedec -Jjmeter.save.saveservice.output_format=xml",
            WINDOWS_JMETER_BIN, config.getScriptPath(), config.getEnv(), config.getCasePath(), config.getResultPath()
        );
        
        log.info("执行JMeter命令: {}", jmeterCommand);
        String jmeterResult = executeCommand(scriptId, jmeterCommand, JMETER_TIMEOUT_SECONDS);
        result.append("JMeter执行结果:\n").append(jmeterResult).append("\n");
        
        // 2. 执行Ant报告生成命令
        String antCommand = String.format(
            "cmd /c cd /d \"%s\" && call .\\ant -Dresult.file=\"%s\" -Dreport.dir=\"%s\" report",
            WINDOWS_ANT_BIN, config.getResultPath(), config.getReportPath()
        );
        
        log.info("执行Ant命令: {}", antCommand);
        String antResult = executeCommand(scriptId, antCommand, ANT_TIMEOUT_SECONDS);
        result.append("Ant执行结果:\n").append(antResult).append("\n");
        
        result.append("报告生成路径: ").append(config.getReportPath()).append("\n");
        return result.toString();
    }
    
    /**
     * 执行Linux环境下的JMeter脚本
     */
    private static String executeLinuxScript(Long scriptId, JMeterConfig config) throws Exception {
        StringBuilder result = new StringBuilder();
        
        // 删除旧的结果文件
        deleteFileIfExists(config.getResultPath());
        result.append("删除旧结果文件: ").append(config.getResultPath()).append("\n");
        
        // 1. 执行JMeter命令
        String jmeterCommand = String.format(
            "cd \"%s\" && ./jmeter.sh -n -t \"%s\" -Jenv=%s -Jcase_path=\"%s\" -l \"%s\" -Jsample_variables=dex,casedec -Jjmeter.save.saveservice.output_format=xml",
            LINUX_JMETER_BIN, config.getScriptPath(), config.getEnv(), config.getCasePath(), config.getResultPath()
        );
        
        log.info("执行JMeter命令: {}", jmeterCommand);
        String jmeterResult = executeCommand(scriptId, jmeterCommand, JMETER_TIMEOUT_SECONDS);
        result.append("JMeter执行结果:\n").append(jmeterResult).append("\n");
        
        // 2. 执行Ant报告生成命令
        String antCommand = String.format(
            "cd \"%s\" && ./ant -Dresult.file=\"%s\" -Dreport.dir=\"%s\" report",
            LINUX_ANT_BIN, config.getResultPath(), config.getReportPath()
        );
        
        log.info("执行Ant命令: {}", antCommand);
        String antResult = executeCommand(scriptId, antCommand, ANT_TIMEOUT_SECONDS);
        result.append("Ant执行结果:\n").append(antResult).append("\n");
        
        result.append("报告生成路径: ").append(config.getReportPath()).append("\n");
        return result.toString();
    }

    /**
     * 执行系统命令
     *
     * @param scriptId 脚本ID（用于任务管理）
     * @param command 命令
     * @param timeoutSeconds 超时时间（秒）
     * @return 执行结果
     * @throws Exception 执行异常
     */
    private static String executeCommand(Long scriptId, String command, int timeoutSeconds) throws Exception {
        StringBuilder output = new StringBuilder();
        StringBuilder errorOutput = new StringBuilder();

        Process process = null;
        BufferedReader reader = null;
        BufferedReader errorReader = null;

        try {
            // 创建进程
            process = Runtime.getRuntime().exec(command);

            // 将进程添加到运行任务管理
            runningTasks.put(scriptId, process);

            // 读取标准输出
            reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "GBK"));
            // 读取错误输出
            errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream(), "GBK"));

            // 等待进程完成，设置超时
            boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("命令执行超时: " + command);
            }

            // 读取输出
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            // 读取错误输出
            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                String errorMsg = "命令执行失败，退出码: " + exitCode +
                                "\n标准输出: " + output.toString() +
                                "\n错误输出: " + errorOutput.toString();
                log.warn(errorMsg);
                // 对于JMeter和Ant，有时候即使成功也可能返回非0退出码，所以这里只记录警告
            }

            // 合并输出和错误输出
            StringBuilder result = new StringBuilder();
            if (StringUtils.isNotEmpty(output.toString())) {
                result.append("标准输出:\n").append(output.toString());
            }
            if (StringUtils.isNotEmpty(errorOutput.toString())) {
                result.append("错误输出:\n").append(errorOutput.toString());
            }

            return result.toString();

        } finally {
            // 关闭资源
            if (reader != null) {
                try { reader.close(); } catch (IOException e) { /* ignore */ }
            }
            if (errorReader != null) {
                try { errorReader.close(); } catch (IOException e) { /* ignore */ }
            }
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }

    /**
     * 删除文件（如果存在）
     *
     * @param filePath 文件路径
     */
    private static void deleteFileIfExists(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                log.info("删除文件 {} : {}", filePath, deleted ? "成功" : "失败");
            }
        } catch (Exception e) {
            log.warn("删除文件失败: {}", filePath, e);
        }
    }

    /**
     * 获取报告文件列表
     *
     * @param reportPath 报告目录路径
     * @return 报告文件列表
     */
    public static String[] getReportFiles(String reportPath) {
        try {
            File reportDir = new File(reportPath);
            if (reportDir.exists() && reportDir.isDirectory()) {
                return reportDir.list();
            }
        } catch (Exception e) {
            log.warn("获取报告文件列表失败: {}", reportPath, e);
        }
        return new String[0];
    }

    /**
     * 检查脚本文件是否存在
     *
     * @param scriptPath 脚本路径
     * @return 是否存在
     */
    public static boolean isScriptFileExists(String scriptPath) {
        try {
            File scriptFile = new File(scriptPath);
            return scriptFile.exists() && scriptFile.isFile();
        } catch (Exception e) {
            log.warn("检查脚本文件失败: {}", scriptPath, e);
            return false;
        }
    }

    /**
     * 检查测试用例文件是否存在
     *
     * @param casePath 测试用例路径
     * @return 是否存在
     */
    public static boolean isCaseFileExists(String casePath) {
        try {
            File caseFile = new File(casePath);
            return caseFile.exists() && caseFile.isFile();
        } catch (Exception e) {
            log.warn("检查测试用例文件失败: {}", casePath, e);
            return false;
        }
    }
}
