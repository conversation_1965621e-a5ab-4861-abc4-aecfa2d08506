package com.ccblife.cases.service.impl;

import java.util.List;
import com.ccblife.common.utils.DateUtils;
import com.ccblife.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ccblife.cases.mapper.AtpReportMapper;
import com.ccblife.cases.domain.AtpReport;
import com.ccblife.cases.service.IAtpReportService;

/**
 * 执行报告Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class AtpReportServiceImpl implements IAtpReportService 
{
    @Autowired
    private AtpReportMapper atpReportMapper;

    /**
     * 查询执行报告
     * 
     * @param reportId 执行报告主键
     * @return 执行报告
     */
    @Override
    public AtpReport selectAtpReportByReportId(Long reportId)
    {
        return atpReportMapper.selectAtpReportByReportId(reportId);
    }

    /**
     * 查询执行报告列表
     * 
     * @param atpReport 执行报告
     * @return 执行报告
     */
    @Override
    public List<AtpReport> selectAtpReportList(AtpReport atpReport)
    {
        return atpReportMapper.selectAtpReportList(atpReport);
    }

    /**
     * 新增执行报告
     * 
     * @param atpReport 执行报告
     * @return 结果
     */
    @Override
    public int insertAtpReport(AtpReport atpReport)
    {
        atpReport.setCreateTime(DateUtils.getNowDate());
        atpReport.setCreateBy(SecurityUtils.getUsername());
        return atpReportMapper.insertAtpReport(atpReport);
    }

    /**
     * 修改执行报告
     * 
     * @param atpReport 执行报告
     * @return 结果
     */
    @Override
    public int updateAtpReport(AtpReport atpReport)
    {
        return atpReportMapper.updateAtpReport(atpReport);
    }

    /**
     * 批量删除执行报告
     * 
     * @param reportIds 需要删除的执行报告主键
     * @return 结果
     */
    @Override
    public int deleteAtpReportByReportIds(Long[] reportIds)
    {
        return atpReportMapper.deleteAtpReportByReportIds(reportIds);
    }

    /**
     * 删除执行报告信息
     * 
     * @param reportId 执行报告主键
     * @return 结果
     */
    @Override
    public int deleteAtpReportByReportId(Long reportId)
    {
        return atpReportMapper.deleteAtpReportByReportId(reportId);
    }
}
