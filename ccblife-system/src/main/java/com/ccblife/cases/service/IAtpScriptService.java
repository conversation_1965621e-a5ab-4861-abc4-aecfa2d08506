package com.ccblife.cases.service;

import java.util.List;
import com.ccblife.cases.domain.AtpScript;
import com.ccblife.common.core.domain.AjaxResult;

/**
 * 自动化测试脚本（存储脚本基础信息及业务属性）Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IAtpScriptService 
{


    /**
     * 执行自动化测试脚本（存储脚本基础信息及业务属性）
     *
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    public AjaxResult exec(AtpScript atpScript);

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param scriptId 自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 自动化测试脚本（存储脚本基础信息及业务属性）
     */
    public AtpScript selectAtpScriptByScriptId(Long scriptId);

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）列表
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 自动化测试脚本（存储脚本基础信息及业务属性）集合
     */
    public List<AtpScript> selectAtpScriptList(AtpScript atpScript);

    /**
     * 新增自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    public int insertAtpScript(AtpScript atpScript);

    /**
     * 修改自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    public int updateAtpScript(AtpScript atpScript);

    /**
     * 批量删除自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param scriptIds 需要删除的自动化测试脚本（存储脚本基础信息及业务属性）主键集合
     * @return 结果
     */
    public int deleteAtpScriptByScriptIds(Long[] scriptIds);

    /**
     * 删除自动化测试脚本（存储脚本基础信息及业务属性）信息
     * 
     * @param scriptId 自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 结果
     */
    public int deleteAtpScriptByScriptId(Long scriptId);
}
