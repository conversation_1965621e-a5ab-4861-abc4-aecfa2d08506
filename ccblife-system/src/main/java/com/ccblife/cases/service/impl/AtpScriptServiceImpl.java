package com.ccblife.cases.service.impl;

import java.util.List;

import com.ccblife.common.core.domain.AjaxResult;
import com.ccblife.common.utils.DateUtils;
import com.ccblife.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ccblife.cases.mapper.AtpScriptMapper;
import com.ccblife.cases.domain.AtpScript;
import com.ccblife.cases.service.IAtpScriptService;

/**
 * 自动化测试脚本（存储脚本基础信息及业务属性）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class AtpScriptServiceImpl implements IAtpScriptService 
{
    @Autowired
    private AtpScriptMapper atpScriptMapper;


    @Override
    public AjaxResult exec(AtpScript atpScript) {
        if (atpScript.getScriptId() == null||atpScript.getScriptId() == 0)
            return AjaxResult.error("请选择要执行的脚本");

        // TODO: 执行脚本
        return AjaxResult.success();
    }

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param scriptId 自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @Override
    public AtpScript selectAtpScriptByScriptId(Long scriptId)
    {
        return atpScriptMapper.selectAtpScriptByScriptId(scriptId);
    }

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）列表
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @Override
    public List<AtpScript> selectAtpScriptList(AtpScript atpScript)
    {
        return atpScriptMapper.selectAtpScriptList(atpScript);
    }

    /**
     * 新增自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    @Override
    public int insertAtpScript(AtpScript atpScript)
    {
        atpScript.setCreateTime(DateUtils.getNowDate());
        atpScript.setCreateBy(SecurityUtils.getUsername());
        return atpScriptMapper.insertAtpScript(atpScript);
    }

    /**
     * 修改自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    @Override
    public int updateAtpScript(AtpScript atpScript)
    {
        atpScript.setUpdateTime(DateUtils.getNowDate());
        atpScript.setUpdateBy(SecurityUtils.getUsername());
        return atpScriptMapper.updateAtpScript(atpScript);
    }

    /**
     * 批量删除自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param scriptIds 需要删除的自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 结果
     */
    @Override
    public int deleteAtpScriptByScriptIds(Long[] scriptIds)
    {
        return atpScriptMapper.deleteAtpScriptByScriptIds(scriptIds);
    }

    /**
     * 删除自动化测试脚本（存储脚本基础信息及业务属性）信息
     * 
     * @param scriptId 自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 结果
     */
    @Override
    public int deleteAtpScriptByScriptId(Long scriptId)
    {
        return atpScriptMapper.deleteAtpScriptByScriptId(scriptId);
    }
}
