package com.ccblife.cases.service.impl;

import java.util.List;

import com.ccblife.cases.util.JMeterExecutorUtil;
import com.ccblife.common.core.domain.AjaxResult;
import com.ccblife.common.utils.DateUtils;
import com.ccblife.common.utils.SecurityUtils;
import com.ccblife.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ccblife.cases.mapper.AtpScriptMapper;
import com.ccblife.cases.domain.AtpScript;
import com.ccblife.cases.service.IAtpScriptService;

/**
 * 自动化测试脚本（存储脚本基础信息及业务属性）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class AtpScriptServiceImpl implements IAtpScriptService
{
    private static final Logger log = LoggerFactory.getLogger(AtpScriptServiceImpl.class);

    @Autowired
    private AtpScriptMapper atpScriptMapper;


    @Override
    public AjaxResult exec(AtpScript atpScript) {
        if (atpScript.getScriptId() == null || atpScript.getScriptId() == 0) {
            return AjaxResult.error("请选择要执行的脚本");
        }

        try {
            // 获取脚本详细信息
            AtpScript scriptInfo = atpScriptMapper.selectAtpScriptByScriptId(atpScript.getScriptId());
            if (scriptInfo == null) {
                return AjaxResult.error("脚本信息不存在");
            }

            log.info("开始执行脚本: {}", scriptInfo.getScriptName());

            // 检查是否已有任务在运行
            if (JMeterExecutorUtil.isTaskRunning(scriptInfo.getScriptId())) {
                return AjaxResult.error("脚本正在执行中，请稍后再试");
            }

            // 创建JMeter配置
            // 这里可以从请求参数或配置中获取环境和用例文件名
            String env = StringUtils.isNotEmpty(atpScript.getRemark()) ? atpScript.getRemark() : "sit1";
            String caseFileName = "1111.txt"; // 可以从参数中获取或根据脚本配置

            JMeterExecutorUtil.JMeterConfig config = JMeterExecutorUtil.createJMeterConfig(scriptInfo, env, caseFileName);

            // 检查脚本文件是否存在
            if (!JMeterExecutorUtil.isScriptFileExists(config.getScriptPath())) {
                return AjaxResult.error("脚本文件不存在: " + config.getScriptPath());
            }

            // 检查测试用例文件是否存在
            if (!JMeterExecutorUtil.isCaseFileExists(config.getCasePath())) {
                return AjaxResult.error("测试用例文件不存在: " + config.getCasePath());
            }

            // 执行脚本
            String result = JMeterExecutorUtil.executeJMeterScript(scriptInfo, config);

            log.info("脚本执行完成: {}", scriptInfo.getScriptName());
            return AjaxResult.success("脚本执行成功", result);

        } catch (Exception e) {
            log.error("执行脚本失败", e);
            return AjaxResult.error("执行脚本失败: " + e.getMessage());
        }
    }

    /**
     * 终止脚本执行
     *
     * @param atpScript 脚本信息
     * @return 结果
     */
    public AjaxResult terminate(AtpScript atpScript) {
        if (atpScript.getScriptId() == null || atpScript.getScriptId() == 0) {
            return AjaxResult.error("请选择要终止的脚本");
        }

        try {
            boolean terminated = JMeterExecutorUtil.terminateTask(atpScript.getScriptId());
            if (terminated) {
                log.info("成功终止脚本执行: {}", atpScript.getScriptId());
                return AjaxResult.success("脚本执行已终止");
            } else {
                return AjaxResult.warn("脚本未在执行中或已执行完成");
            }
        } catch (Exception e) {
            log.error("终止脚本执行失败", e);
            return AjaxResult.error("终止脚本执行失败: " + e.getMessage());
        }
    }

    /**
     * 检查脚本执行状态
     *
     * @param atpScript 脚本信息
     * @return 结果
     */
    public AjaxResult status(AtpScript atpScript) {
        if (atpScript.getScriptId() == null || atpScript.getScriptId() == 0) {
            return AjaxResult.error("请选择要查询的脚本");
        }

        try {
            boolean isRunning = JMeterExecutorUtil.isTaskRunning(atpScript.getScriptId());
            return AjaxResult.success("查询成功", isRunning ? "执行中" : "未执行");
        } catch (Exception e) {
            log.error("查询脚本状态失败", e);
            return AjaxResult.error("查询脚本状态失败: " + e.getMessage());
        }
    }

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param scriptId 自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @Override
    public AtpScript selectAtpScriptByScriptId(Long scriptId)
    {
        return atpScriptMapper.selectAtpScriptByScriptId(scriptId);
    }

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）列表
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @Override
    public List<AtpScript> selectAtpScriptList(AtpScript atpScript)
    {
        return atpScriptMapper.selectAtpScriptList(atpScript);
    }

    /**
     * 新增自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    @Override
    public int insertAtpScript(AtpScript atpScript)
    {
        atpScript.setCreateTime(DateUtils.getNowDate());
        atpScript.setCreateBy(SecurityUtils.getUsername());
        return atpScriptMapper.insertAtpScript(atpScript);
    }

    /**
     * 修改自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    @Override
    public int updateAtpScript(AtpScript atpScript)
    {
        atpScript.setUpdateTime(DateUtils.getNowDate());
        atpScript.setUpdateBy(SecurityUtils.getUsername());
        return atpScriptMapper.updateAtpScript(atpScript);
    }

    /**
     * 批量删除自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param scriptIds 需要删除的自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 结果
     */
    @Override
    public int deleteAtpScriptByScriptIds(Long[] scriptIds)
    {
        return atpScriptMapper.deleteAtpScriptByScriptIds(scriptIds);
    }

    /**
     * 删除自动化测试脚本（存储脚本基础信息及业务属性）信息
     *
     * @param scriptId 自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 结果
     */
    @Override
    public int deleteAtpScriptByScriptId(Long scriptId)
    {
        return atpScriptMapper.deleteAtpScriptByScriptId(scriptId);
    }
}
