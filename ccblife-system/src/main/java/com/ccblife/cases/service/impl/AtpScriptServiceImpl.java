package com.ccblife.cases.service.impl;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.ccblife.common.core.domain.AjaxResult;
import com.ccblife.common.utils.DateUtils;
import com.ccblife.common.utils.SecurityUtils;
import com.ccblife.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ccblife.cases.mapper.AtpScriptMapper;
import com.ccblife.cases.domain.AtpScript;
import com.ccblife.cases.service.IAtpScriptService;

/**
 * 自动化测试脚本（存储脚本基础信息及业务属性）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class AtpScriptServiceImpl implements IAtpScriptService
{
    private static final Logger log = LoggerFactory.getLogger(AtpScriptServiceImpl.class);

    @Autowired
    private AtpScriptMapper atpScriptMapper;


    @Override
    public AjaxResult exec(AtpScript atpScript) {
        if (atpScript.getScriptId() == null || atpScript.getScriptId() == 0) {
            return AjaxResult.error("请选择要执行的脚本");
        }

        try {
            // 获取脚本详细信息
            AtpScript scriptInfo = atpScriptMapper.selectAtpScriptByScriptId(atpScript.getScriptId());
            if (scriptInfo == null) {
                return AjaxResult.error("脚本信息不存在");
            }

            log.info("开始执行脚本: {}", scriptInfo.getScriptName());

            // 检测运行环境
            boolean isWindows = isWindowsEnvironment();
            log.info("检测到运行环境: {}", isWindows ? "Windows" : "Linux");

            // 执行脚本
            String result = executeJMeterScript(scriptInfo, isWindows);

            log.info("脚本执行完成: {}", scriptInfo.getScriptName());
            return AjaxResult.success("脚本执行成功", result);

        } catch (Exception e) {
            log.error("执行脚本失败", e);
            return AjaxResult.error("执行脚本失败: " + e.getMessage());
        }
    }

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param scriptId 自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @Override
    public AtpScript selectAtpScriptByScriptId(Long scriptId)
    {
        return atpScriptMapper.selectAtpScriptByScriptId(scriptId);
    }

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）列表
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @Override
    public List<AtpScript> selectAtpScriptList(AtpScript atpScript)
    {
        return atpScriptMapper.selectAtpScriptList(atpScript);
    }

    /**
     * 新增自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    @Override
    public int insertAtpScript(AtpScript atpScript)
    {
        atpScript.setCreateTime(DateUtils.getNowDate());
        atpScript.setCreateBy(SecurityUtils.getUsername());
        return atpScriptMapper.insertAtpScript(atpScript);
    }

    /**
     * 修改自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param atpScript 自动化测试脚本（存储脚本基础信息及业务属性）
     * @return 结果
     */
    @Override
    public int updateAtpScript(AtpScript atpScript)
    {
        atpScript.setUpdateTime(DateUtils.getNowDate());
        atpScript.setUpdateBy(SecurityUtils.getUsername());
        return atpScriptMapper.updateAtpScript(atpScript);
    }

    /**
     * 批量删除自动化测试脚本（存储脚本基础信息及业务属性）
     * 
     * @param scriptIds 需要删除的自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 结果
     */
    @Override
    public int deleteAtpScriptByScriptIds(Long[] scriptIds)
    {
        return atpScriptMapper.deleteAtpScriptByScriptIds(scriptIds);
    }

    /**
     * 删除自动化测试脚本（存储脚本基础信息及业务属性）信息
     *
     * @param scriptId 自动化测试脚本（存储脚本基础信息及业务属性）主键
     * @return 结果
     */
    @Override
    public int deleteAtpScriptByScriptId(Long scriptId)
    {
        return atpScriptMapper.deleteAtpScriptByScriptId(scriptId);
    }

    /**
     * 检测是否为Windows环境
     *
     * @return true-Windows环境，false-Linux环境
     */
    private boolean isWindowsEnvironment() {
        String osName = System.getProperty("os.name");
        return osName != null && osName.toLowerCase().contains("windows");
    }

    /**
     * 执行JMeter脚本
     *
     * @param scriptInfo 脚本信息
     * @param isWindows 是否为Windows环境
     * @return 执行结果
     * @throws Exception 执行异常
     */
    private String executeJMeterScript(AtpScript scriptInfo, boolean isWindows) throws Exception {
        StringBuilder result = new StringBuilder();

        if (isWindows) {
            result.append(executeWindowsScript(scriptInfo));
        } else {
            result.append(executeLinuxScript(scriptInfo));
        }

        return result.toString();
    }

    /**
     * 执行Windows环境下的脚本
     *
     * @param scriptInfo 脚本信息
     * @return 执行结果
     * @throws Exception 执行异常
     */
    private String executeWindowsScript(AtpScript scriptInfo) throws Exception {
        StringBuilder result = new StringBuilder();

        // Windows环境路径配置
        String tempPath = "E:\\Project\\Temp";
        String jmeterPath = tempPath + "\\apache-jmeter-5.6.3\\bin";
        String antPath = tempPath + "\\apache-jmeter-5.6.3\\apache-ant-1.10.15\\bin";
        String resultXmlPath = tempPath + "\\jmeter\\result.xml";
        String jmxPath = tempPath + "\\jmeter\\jenkins.jmx";
        String casePath = tempPath + "\\jmeter\\1111.txt";

        // 1. 删除旧的结果文件
        deleteFileIfExists(resultXmlPath);
        result.append("删除旧结果文件: ").append(resultXmlPath).append("\n");

        // 2. 执行JMeter命令
        String jmeterCommand = String.format(
            "cmd /c cd /d \"%s\" && call .\\jmeter.bat -n -t \"%s\" -Jenv=sit1 -Jcase_path=\"%s\" -l \"%s\" -Jsample_variables=dex,casedec -Jjmeter.save.saveservice.output_format=xml",
            jmeterPath, jmxPath, casePath, resultXmlPath
        );

        log.info("执行JMeter命令: {}", jmeterCommand);
        String jmeterResult = executeCommand(jmeterCommand, 300); // 5分钟超时
        result.append("JMeter执行结果:\n").append(jmeterResult).append("\n");

        // 3. 执行Ant报告生成命令
        String antCommand = String.format(
            "cmd /c cd /d \"%s\" && call .\\ant report",
            antPath
        );

        log.info("执行Ant命令: {}", antCommand);
        String antResult = executeCommand(antCommand, 120); // 2分钟超时
        result.append("Ant执行结果:\n").append(antResult).append("\n");

        return result.toString();
    }

    /**
     * 执行Linux环境下的脚本
     *
     * @param scriptInfo 脚本信息
     * @return 执行结果
     * @throws Exception 执行异常
     */
    private String executeLinuxScript(AtpScript scriptInfo) throws Exception {
        StringBuilder result = new StringBuilder();

        // Linux环境路径配置（Docker容器内）
        String tempPath = "/app/temp";
        String jmeterPath = tempPath + "/apache-jmeter-5.6.3/bin";
        String antPath = tempPath + "/apache-jmeter-5.6.3/apache-ant-1.10.15/bin";
        String resultXmlPath = tempPath + "/jmeter/result.xml";
        String jmxPath = tempPath + "/jmeter/jenkins.jmx";
        String casePath = tempPath + "/jmeter/1111.txt";

        // 1. 删除旧的结果文件
        deleteFileIfExists(resultXmlPath);
        result.append("删除旧结果文件: ").append(resultXmlPath).append("\n");

        // 2. 执行JMeter命令
        String jmeterCommand = String.format(
            "cd \"%s\" && ./jmeter.sh -n -t \"%s\" -Jenv=sit1 -Jcase_path=\"%s\" -l \"%s\" -Jsample_variables=dex,casedec -Jjmeter.save.saveservice.output_format=xml",
            jmeterPath, jmxPath, casePath, resultXmlPath
        );

        log.info("执行JMeter命令: {}", jmeterCommand);
        String jmeterResult = executeCommand(jmeterCommand, 300); // 5分钟超时
        result.append("JMeter执行结果:\n").append(jmeterResult).append("\n");

        // 3. 执行Ant报告生成命令
        String antCommand = String.format(
            "cd \"%s\" && ./ant report",
            antPath
        );

        log.info("执行Ant命令: {}", antCommand);
        String antResult = executeCommand(antCommand, 120); // 2分钟超时
        result.append("Ant执行结果:\n").append(antResult).append("\n");

        return result.toString();
    }

    /**
     * 执行系统命令
     *
     * @param command 命令
     * @param timeoutSeconds 超时时间（秒）
     * @return 执行结果
     * @throws Exception 执行异常
     */
    private String executeCommand(String command, int timeoutSeconds) throws Exception {
        StringBuilder output = new StringBuilder();
        StringBuilder errorOutput = new StringBuilder();

        Process process = null;
        BufferedReader reader = null;
        BufferedReader errorReader = null;

        try {
            // 创建进程
            process = Runtime.getRuntime().exec(command);

            // 读取标准输出
            reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "GBK"));
            // 读取错误输出
            errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream(), "GBK"));

            // 等待进程完成，设置超时
            boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("命令执行超时: " + command);
            }

            // 读取输出
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            // 读取错误输出
            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                String errorMsg = "命令执行失败，退出码: " + exitCode +
                                "\n标准输出: " + output.toString() +
                                "\n错误输出: " + errorOutput.toString();
                log.warn(errorMsg);
                // 对于JMeter和Ant，有时候即使成功也可能返回非0退出码，所以这里只记录警告
            }

            // 合并输出和错误输出
            StringBuilder result = new StringBuilder();
            if (StringUtils.isNotEmpty(output.toString())) {
                result.append("标准输出:\n").append(output.toString());
            }
            if (StringUtils.isNotEmpty(errorOutput.toString())) {
                result.append("错误输出:\n").append(errorOutput.toString());
            }

            return result.toString();

        } finally {
            // 关闭资源
            if (reader != null) {
                try { reader.close(); } catch (IOException e) { /* ignore */ }
            }
            if (errorReader != null) {
                try { errorReader.close(); } catch (IOException e) { /* ignore */ }
            }
            if (process != null) {
                process.destroyForcibly();
            }
        }
    }

    /**
     * 删除文件（如果存在）
     *
     * @param filePath 文件路径
     */
    private void deleteFileIfExists(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                log.info("删除文件 {} : {}", filePath, deleted ? "成功" : "失败");
            }
        } catch (Exception e) {
            log.warn("删除文件失败: {}", filePath, e);
        }
    }
}
