package com.ccblife.cases.domain;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ccblife.common.annotation.Excel;
import com.ccblife.common.core.domain.BaseEntity;

/**
 * 执行报告对象 atp_report
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public class AtpReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long reportId;

    /** 脚本名称 */
    @Excel(name = "脚本名称")
    private Long scriptId;

    /** 报告名称 */
    @Excel(name = "报告名称")
    private String reportName;

    /** 执行环境 */
    @Excel(name = "执行环境")
    private String execEnvCode;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date execStartTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date execEndTime;

    /** 执行状态 */
    @Excel(name = "执行状态")
    private String execStatusCode;

    /** 总案例数 */
    @Excel(name = "总案例数")
    private Integer totalCases;

    /** 通过案例数 */
    @Excel(name = "通过案例数")
    private Integer passCases;

    /** 失败案例数 */
    @Excel(name = "失败案例数")
    private Integer failCases;

    /** 通过率 */
    @Excel(name = "通过率")
    private BigDecimal passRate;

    /** 失败原因 */
    @Excel(name = "失败原因")
    private String failReason;

    /** 执行案例 */
    @Excel(name = "执行案例")
    private String execScriptPath;

    /** 执行报告 */
    @Excel(name = "执行报告")
    private String execReportPath;

    public void setReportId(Long reportId) 
    {
        this.reportId = reportId;
    }

    public Long getReportId() 
    {
        return reportId;
    }
    public void setScriptId(Long scriptId) 
    {
        this.scriptId = scriptId;
    }

    public Long getScriptId() 
    {
        return scriptId;
    }
    public void setReportName(String reportName) 
    {
        this.reportName = reportName;
    }

    public String getReportName() 
    {
        return reportName;
    }
    public void setExecEnvCode(String execEnvCode) 
    {
        this.execEnvCode = execEnvCode;
    }

    public String getExecEnvCode() 
    {
        return execEnvCode;
    }
    public void setExecStartTime(Date execStartTime)
    {
        this.execStartTime = execStartTime;
    }

    public Date getExecStartTime()
    {
        return execStartTime;
    }
    public void setExecEndTime(Date execEndTime)
    {
        this.execEndTime = execEndTime;
    }

    public Date getExecEndTime()
    {
        return execEndTime;
    }
    public void setExecStatusCode(String execStatusCode) 
    {
        this.execStatusCode = execStatusCode;
    }

    public String getExecStatusCode() 
    {
        return execStatusCode;
    }
    public void setTotalCases(Integer totalCases) 
    {
        this.totalCases = totalCases;
    }

    public Integer getTotalCases() 
    {
        return totalCases;
    }
    public void setPassCases(Integer passCases) 
    {
        this.passCases = passCases;
    }

    public Integer getPassCases() 
    {
        return passCases;
    }
    public void setFailCases(Integer failCases) 
    {
        this.failCases = failCases;
    }

    public Integer getFailCases() 
    {
        return failCases;
    }
    public void setPassRate(BigDecimal passRate) 
    {
        this.passRate = passRate;
    }

    public BigDecimal getPassRate() 
    {
        return passRate;
    }
    public void setFailReason(String failReason) 
    {
        this.failReason = failReason;
    }

    public String getFailReason() 
    {
        return failReason;
    }
    public void setExecScriptPath(String execScriptPath) 
    {
        this.execScriptPath = execScriptPath;
    }

    public String getExecScriptPath() 
    {
        return execScriptPath;
    }
    public void setExecReportPath(String execReportPath) 
    {
        this.execReportPath = execReportPath;
    }

    public String getExecReportPath() 
    {
        return execReportPath;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("reportId", getReportId())
            .append("scriptId", getScriptId())
            .append("reportName", getReportName())
            .append("execEnvCode", getExecEnvCode())
            .append("execStartTime", getExecStartTime())
            .append("execEndTime", getExecEndTime())
            .append("execStatusCode", getExecStatusCode())
            .append("totalCases", getTotalCases())
            .append("passCases", getPassCases())
            .append("failCases", getFailCases())
            .append("passRate", getPassRate())
            .append("failReason", getFailReason())
            .append("execScriptPath", getExecScriptPath())
            .append("execReportPath", getExecReportPath())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}