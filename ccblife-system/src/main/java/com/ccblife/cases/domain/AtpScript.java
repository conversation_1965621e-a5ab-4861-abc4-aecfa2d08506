package com.ccblife.cases.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ccblife.common.annotation.Excel;
import com.ccblife.common.core.domain.BaseEntity;

/**
 * 自动化测试脚本（存储脚本基础信息及业务属性）对象 atp_script
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public class AtpScript extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Excel(name = "主键")
    private Long scriptId;

    /** 业务场景 */
    @Excel(name = "业务场景")
    private String scenarioCode;

    /** 业务渠道 */
    @Excel(name = "业务渠道")
    private String channelCode;

    /** 脚本全称 */
    @Excel(name = "脚本全称")
    private String scriptName;

    /** 脚本简称 */
    @Excel(name = "脚本简称")
    private String scriptShortName;

    /** 脚本路径 */
    @Excel(name = "脚本路径")
    private String scriptPath;

    /** 脚本模板路径 */
    @Excel(name = "脚本模板路径")
    private String templatePath;

    /** 删除标志 */
    private String delFlag;

    public void setScriptId(Long scriptId) 
    {
        this.scriptId = scriptId;
    }

    public Long getScriptId() 
    {
        return scriptId;
    }
    public void setScenarioCode(String scenarioCode) 
    {
        this.scenarioCode = scenarioCode;
    }

    public String getScenarioCode() 
    {
        return scenarioCode;
    }
    public void setChannelCode(String channelCode) 
    {
        this.channelCode = channelCode;
    }

    public String getChannelCode() 
    {
        return channelCode;
    }
    public void setScriptName(String scriptName) 
    {
        this.scriptName = scriptName;
    }

    public String getScriptName() 
    {
        return scriptName;
    }
    public void setScriptShortName(String scriptShortName) 
    {
        this.scriptShortName = scriptShortName;
    }

    public String getScriptShortName() 
    {
        return scriptShortName;
    }
    public void setScriptPath(String scriptPath) 
    {
        this.scriptPath = scriptPath;
    }

    public String getScriptPath() 
    {
        return scriptPath;
    }
    public void setTemplatePath(String templatePath) 
    {
        this.templatePath = templatePath;
    }

    public String getTemplatePath() 
    {
        return templatePath;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("scriptId", getScriptId())
            .append("scenarioCode", getScenarioCode())
            .append("channelCode", getChannelCode())
            .append("scriptName", getScriptName())
            .append("scriptShortName", getScriptShortName())
            .append("scriptPath", getScriptPath())
            .append("templatePath", getTemplatePath())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
