package com.ccblife.cases.mapper;

import java.util.List;
import com.ccblife.cases.domain.AtpReport;

/**
 * 执行报告Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface AtpReportMapper 
{
    /**
     * 查询执行报告
     * 
     * @param reportId 执行报告主键
     * @return 执行报告
     */
    public AtpReport selectAtpReportByReportId(Long reportId);

    /**
     * 查询执行报告列表
     * 
     * @param atpReport 执行报告
     * @return 执行报告集合
     */
    public List<AtpReport> selectAtpReportList(AtpReport atpReport);

    /**
     * 新增执行报告
     * 
     * @param atpReport 执行报告
     * @return 结果
     */
    public int insertAtpReport(AtpReport atpReport);

    /**
     * 修改执行报告
     * 
     * @param atpReport 执行报告
     * @return 结果
     */
    public int updateAtpReport(AtpReport atpReport);

    /**
     * 删除执行报告
     * 
     * @param reportId 执行报告主键
     * @return 结果
     */
    public int deleteAtpReportByReportId(Long reportId);

    /**
     * 批量删除执行报告
     * 
     * @param reportIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAtpReportByReportIds(Long[] reportIds);
}
