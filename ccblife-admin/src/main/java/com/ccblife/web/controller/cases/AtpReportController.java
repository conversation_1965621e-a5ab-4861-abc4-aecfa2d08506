package com.ccblife.cases.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ccblife.common.annotation.Log;
import com.ccblife.common.core.controller.BaseController;
import com.ccblife.common.core.domain.AjaxResult;
import com.ccblife.common.enums.BusinessType;
import com.ccblife.cases.domain.AtpReport;
import com.ccblife.cases.service.IAtpReportService;
import com.ccblife.common.utils.poi.ExcelUtil;
import com.ccblife.common.core.page.TableDataInfo;

/**
 * 执行报告Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/cases/report")
public class AtpReportController extends BaseController
{
    @Autowired
    private IAtpReportService atpReportService;

    /**
     * 查询执行报告列表
     */
    @PreAuthorize("@ss.hasPermi('cases:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(AtpReport atpReport)
    {
        startPage();
        List<AtpReport> list = atpReportService.selectAtpReportList(atpReport);
        return getDataTable(list);
    }

    /**
     * 导出执行报告列表
     */
    @PreAuthorize("@ss.hasPermi('cases:report:export')")
    @Log(title = "执行报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AtpReport atpReport)
    {
        List<AtpReport> list = atpReportService.selectAtpReportList(atpReport);
        ExcelUtil<AtpReport> util = new ExcelUtil<AtpReport>(AtpReport.class);
        util.exportExcel(response, list, "执行报告数据");
    }

    /**
     * 获取执行报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('cases:report:query')")
    @GetMapping(value = "/{reportId}")
    public AjaxResult getInfo(@PathVariable("reportId") Long reportId)
    {
        return success(atpReportService.selectAtpReportByReportId(reportId));
    }

    /**
     * 新增执行报告
     */
    @PreAuthorize("@ss.hasPermi('cases:report:add')")
    @Log(title = "执行报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AtpReport atpReport)
    {
        return toAjax(atpReportService.insertAtpReport(atpReport));
    }

    /**
     * 修改执行报告
     */
    @PreAuthorize("@ss.hasPermi('cases:report:edit')")
    @Log(title = "执行报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AtpReport atpReport)
    {
        return toAjax(atpReportService.updateAtpReport(atpReport));
    }

    /**
     * 删除执行报告
     */
    @PreAuthorize("@ss.hasPermi('cases:report:remove')")
    @Log(title = "执行报告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reportIds}")
    public AjaxResult remove(@PathVariable Long[] reportIds)
    {
        return toAjax(atpReportService.deleteAtpReportByReportIds(reportIds));
    }
}
