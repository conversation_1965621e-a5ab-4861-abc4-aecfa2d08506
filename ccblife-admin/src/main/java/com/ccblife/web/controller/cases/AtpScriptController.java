package com.ccblife.cases.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ccblife.common.annotation.Log;
import com.ccblife.common.core.controller.BaseController;
import com.ccblife.common.core.domain.AjaxResult;
import com.ccblife.common.enums.BusinessType;
import com.ccblife.cases.domain.AtpScript;
import com.ccblife.cases.service.IAtpScriptService;
import com.ccblife.common.utils.poi.ExcelUtil;
import com.ccblife.common.core.page.TableDataInfo;

/**
 * 自动化测试脚本（存储脚本基础信息及业务属性）Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/cases/script")
public class AtpScriptController extends BaseController
{
    @Autowired
    private IAtpScriptService atpScriptService;

    /**
     * 执行自动化测试脚本
     */
    @PreAuthorize("@ss.hasPermi('cases:script:exec')")
    @Log(title = "自动化测试脚本执行", businessType = BusinessType.OTHER)
    @PostMapping("/exec")
    public AjaxResult exec(@RequestBody AtpScript atpScript)
    {
        return atpScriptService.exec(atpScript);
    }

    /**
     * 终止自动化测试脚本执行
     */
    @PreAuthorize("@ss.hasPermi('cases:script:exec')")
    @Log(title = "终止自动化测试脚本执行", businessType = BusinessType.OTHER)
    @PostMapping("/terminate")
    public AjaxResult terminate(@RequestBody AtpScript atpScript)
    {
        return atpScriptService.terminate(atpScript);
    }

    /**
     * 查询自动化测试脚本执行状态
     */
    @PreAuthorize("@ss.hasPermi('cases:script:exec')")
    @GetMapping("/status/{scriptId}")
    public AjaxResult status(@PathVariable("scriptId") Long scriptId)
    {
        AtpScript atpScript = new AtpScript();
        atpScript.setScriptId(scriptId);
        return atpScriptService.status(atpScript);
    }

    /**
     * 查询自动化测试脚本（存储脚本基础信息及业务属性）列表
     */
    @PreAuthorize("@ss.hasPermi('cases:script:list')")
    @GetMapping("/list")
    public TableDataInfo list(AtpScript atpScript)
    {
        startPage();
        List<AtpScript> list = atpScriptService.selectAtpScriptList(atpScript);
        return getDataTable(list);
    }

    /**
     * 导出自动化测试脚本（存储脚本基础信息及业务属性）列表
     */
    @PreAuthorize("@ss.hasPermi('cases:script:export')")
    @Log(title = "自动化测试脚本（存储脚本基础信息及业务属性）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AtpScript atpScript)
    {
        List<AtpScript> list = atpScriptService.selectAtpScriptList(atpScript);
        ExcelUtil<AtpScript> util = new ExcelUtil<AtpScript>(AtpScript.class);
        util.exportExcel(response, list, "自动化测试脚本（存储脚本基础信息及业务属性）数据");
    }

    /**
     * 获取自动化测试脚本（存储脚本基础信息及业务属性）详细信息
     */
    @PreAuthorize("@ss.hasPermi('cases:script:query')")
    @GetMapping(value = "/{scriptId}")
    public AjaxResult getInfo(@PathVariable("scriptId") Long scriptId)
    {
        return success(atpScriptService.selectAtpScriptByScriptId(scriptId));
    }

    /**
     * 新增自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @PreAuthorize("@ss.hasPermi('cases:script:add')")
    @Log(title = "自动化测试脚本（存储脚本基础信息及业务属性）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AtpScript atpScript)
    {
        return toAjax(atpScriptService.insertAtpScript(atpScript));
    }

    /**
     * 修改自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @PreAuthorize("@ss.hasPermi('cases:script:edit')")
    @Log(title = "自动化测试脚本（存储脚本基础信息及业务属性）", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AtpScript atpScript)
    {
        return toAjax(atpScriptService.updateAtpScript(atpScript));
    }

    /**
     * 删除自动化测试脚本（存储脚本基础信息及业务属性）
     */
    @PreAuthorize("@ss.hasPermi('cases:script:remove')")
    @Log(title = "自动化测试脚本（存储脚本基础信息及业务属性）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scriptIds}")
    public AjaxResult remove(@PathVariable Long[] scriptIds)
    {
        return toAjax(atpScriptService.deleteAtpScriptByScriptIds(scriptIds));
    }
}
